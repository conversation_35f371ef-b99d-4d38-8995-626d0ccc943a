"""
使用Hugging Face Transformers框架的推理模块
替代原始inference.py中的vLLM相关功能，保持接口兼容性
"""

import json
import copy
from PIL import Image
from pypdf import PdfReader
from ocrflux.image_utils import get_page_image
from ocrflux.table_format import table_matrix2html
from ocrflux.prompts import PageResponse, build_page_to_markdown_prompt, build_element_merge_detect_prompt, build_html_table_merge_prompt


class SamplingParams:
    """
    采样参数类，兼容vLLM的SamplingParams接口
    """
    def __init__(self, temperature=0.0, max_tokens=8192):
        self.temperature = temperature
        self.max_tokens = max_tokens


def build_qwen2_5_vl_prompt(question):
    """
    构建Qwen2.5-VL格式的提示词
    
    Args:
        question: 问题文本
        
    Returns:
        str: 格式化的提示词（现在直接返回原始问题，让processor处理格式）
    """
    # 直接返回问题文本，让processor的apply_chat_template处理格式
    return question


def build_page_to_markdown_query(file_path: str, page_number: int, target_longest_image_dim: int = 1024, image_rotation: int = 0) -> dict:
    """
    构建页面到Markdown的查询
    
    Args:
        file_path: 文件路径
        page_number: 页码
        target_longest_image_dim: 目标图像最长边尺寸
        image_rotation: 图像旋转角度
        
    Returns:
        dict: 查询字典，包含prompt和multi_modal_data
    """
    assert image_rotation in [0, 90, 180, 270], "Invalid image rotation provided in build_page_query"
    image = get_page_image(file_path, page_number, target_longest_image_dim=target_longest_image_dim, image_rotation=image_rotation)
    question = build_page_to_markdown_prompt()
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def build_element_merge_detect_query(text_list_1, text_list_2) -> dict:
    """
    构建元素合并检测查询
    
    Args:
        text_list_1: 文本列表1
        text_list_2: 文本列表2
        
    Returns:
        dict: 查询字典
    """
    image = Image.new('RGB', (28, 28), color='black')
    question = build_element_merge_detect_prompt(text_list_1, text_list_2)
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def build_html_table_merge_query(text_1, text_2) -> dict:
    """
    构建HTML表格合并查询
    
    Args:
        text_1: 文本1
        text_2: 文本2
        
    Returns:
        dict: 查询字典
    """
    image = Image.new('RGB', (28, 28), color='black')
    question = build_html_table_merge_prompt(text_1, text_2)
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def bulid_document_text(page_to_markdown_result, element_merge_detect_result, html_table_merge_result):
    """
    构建文档文本
    
    Args:
        page_to_markdown_result: 页面到Markdown结果
        element_merge_detect_result: 元素合并检测结果
        html_table_merge_result: HTML表格合并结果
        
    Returns:
        str: 构建的文档文本
    """
    page_to_markdown_keys = list(page_to_markdown_result.keys())
    page_to_markdown_keys.sort()
    
    document_text = ""
    for page_num in page_to_markdown_keys:
        markdown_element_list = page_to_markdown_result[page_num]
        
        # 处理跨页合并
        if page_num > 1 and element_merge_detect_result.get((page_num-1, page_num)):
            # 合并逻辑
            pass
        
        # 处理HTML表格合并
        merged_elements = []
        i = 0
        while i < len(markdown_element_list):
            current_element = markdown_element_list[i]
            if i < len(markdown_element_list) - 1:
                next_element = markdown_element_list[i + 1]
                merge_key = (page_num, i, i + 1)
                if merge_key in html_table_merge_result:
                    merged_elements.append(html_table_merge_result[merge_key])
                    i += 2  # 跳过下一个元素
                    continue
            merged_elements.append(current_element)
            i += 1
        
        # 添加到文档文本
        for element in merged_elements:
            if element.strip():
                document_text += element + "\n\n"
    
    return document_text.strip()


def parse(llm, file_path, skip_cross_page_merge=False, max_page_retries=0):
    """
    解析文档，兼容vLLM和Transformers框架
    
    Args:
        llm: 语言模型实例（支持generate方法）
        file_path: 文件路径
        skip_cross_page_merge: 是否跳过跨页合并
        max_page_retries: 最大页面重试次数
        
    Returns:
        dict: 解析结果，包含document_text字段
    """
    print(f"🔍 开始解析文档: {file_path}")
    
    # 创建采样参数
    sampling_params = SamplingParams(temperature=0.0, max_tokens=8192)
    
    # 确定页数
    if file_path.lower().endswith(".pdf"):
        try:
            reader = PdfReader(file_path)
            num_pages = reader.get_num_pages()
            print(f"📄 PDF文档，共 {num_pages} 页")
        except Exception as e:
            print(f"❌ 读取PDF失败: {e}")
            return None
    else:
        num_pages = 1
        print(f"🖼️ 图像文档，1页")
    
    try:
        # Stage 1: Page to Markdown
        print("🔄 阶段1: 页面转Markdown...")
        page_to_markdown_query_list = [build_page_to_markdown_query(file_path, page_num) for page_num in range(1, num_pages + 1)]
        
        print(f"📝 生成 {len(page_to_markdown_query_list)} 个查询")
        responses = llm.generate(page_to_markdown_query_list, sampling_params=sampling_params)
        results = [response.outputs[0].text for response in responses]
        
        page_to_markdown_result = {}
        retry_list = []
        
        for i, result in enumerate(results):
            try:
                # 检查是否为错误响应
                if result.startswith("ERROR:"):
                    print(f"⚠️ 页面 {i+1} 处理出错: {result}")
                    retry_list.append(i)
                    continue
                
                json_data = json.loads(result)
                page_response = PageResponse(**json_data)
                natural_text = page_response.natural_text
                markdown_element_list = []
                
                for text in natural_text.split('\n\n'):
                    if text.startswith("<Image>") and text.endswith("</Image>"):
                        pass  # 跳过图像标记
                    elif text.startswith("<table>") and text.endswith("</table>"):
                        try:
                            new_text = table_matrix2html(text)
                        except:
                            new_text = text.replace("<t>", "").replace("<l>", "").replace("<lt>", "")
                        markdown_element_list.append(new_text)
                    else:
                        markdown_element_list.append(text)
                
                page_to_markdown_result[i+1] = markdown_element_list
                print(f"✅ 页面 {i+1} 处理成功")
                
            except Exception as e:
                print(f"⚠️ 页面 {i+1} JSON解析失败: {e}")
                retry_list.append(i)
        
        # 重试失败的页面
        attempt = 0
        while len(retry_list) > 0 and attempt < max_page_retries:
            print(f"🔄 重试第 {attempt+1} 次，剩余 {len(retry_list)} 页")
            retry_page_to_markdown_query_list = [build_page_to_markdown_query(file_path, page_num+1) for page_num in retry_list]
            retry_sampling_params = SamplingParams(temperature=0.1*attempt, max_tokens=8192)
            responses = llm.generate(retry_page_to_markdown_query_list, sampling_params=retry_sampling_params)
            results = [response.outputs[0].text for response in responses]
            next_retry_list = []
            
            for i, result in zip(retry_list, results):
                try:
                    if result.startswith("ERROR:"):
                        next_retry_list.append(i)
                        continue
                    
                    json_data = json.loads(result)
                    page_response = PageResponse(**json_data)
                    natural_text = page_response.natural_text
                    markdown_element_list = []
                    
                    for text in natural_text.split('\n\n'):
                        if text.startswith("<Image>") and text.endswith("</Image>"):
                            pass
                        elif text.startswith("<table>") and text.endswith("</table>"):
                            try:
                                new_text = table_matrix2html(text)
                            except:
                                new_text = text.replace("<t>", "").replace("<l>", "").replace("<lt>", "")
                            markdown_element_list.append(new_text)
                        else:
                            markdown_element_list.append(text)
                    
                    page_to_markdown_result[i+1] = markdown_element_list
                    print(f"✅ 重试页面 {i+1} 成功")
                    
                except Exception as e:
                    print(f"⚠️ 重试页面 {i+1} 仍然失败: {e}")
                    next_retry_list.append(i)
            
            retry_list = next_retry_list
            attempt += 1
        
        if len(retry_list) > 0:
            print(f"⚠️ {len(retry_list)} 页最终处理失败")
        
        # Stage 2: Element Merge Detection (如果不跳过跨页合并)
        element_merge_detect_result = {}
        if not skip_cross_page_merge and num_pages > 1:
            print("🔄 阶段2: 元素合并检测...")
            element_merge_detect_query_list = []
            element_merge_detect_query_page_pairs = []
            
            for page_num in range(1, num_pages):
                if page_num in page_to_markdown_result and (page_num + 1) in page_to_markdown_result:
                    text_list_1 = page_to_markdown_result[page_num]
                    text_list_2 = page_to_markdown_result[page_num + 1]
                    if len(text_list_1) > 0 and len(text_list_2) > 0:
                        query = build_element_merge_detect_query(text_list_1, text_list_2)
                        element_merge_detect_query_list.append(query)
                        element_merge_detect_query_page_pairs.append((page_num, page_num + 1))
            
            if element_merge_detect_query_list:
                responses = llm.generate(element_merge_detect_query_list, sampling_params=sampling_params)
                results = [response.outputs[0].text for response in responses]
                
                for (page_num_1, page_num_2), result in zip(element_merge_detect_query_page_pairs, results):
                    try:
                        if not result.startswith("ERROR:"):
                            element_merge_detect_result[(page_num_1, page_num_2)] = result.strip().lower() == "yes"
                    except:
                        element_merge_detect_result[(page_num_1, page_num_2)] = False
        
        # Stage 3: HTML Table Merge
        print("🔄 阶段3: HTML表格合并...")
        html_table_merge_result = {}
        html_table_merge_query_list = []
        html_table_merge_query_keys = []
        
        for page_num in page_to_markdown_result:
            markdown_element_list = page_to_markdown_result[page_num]
            for i in range(len(markdown_element_list) - 1):
                text_1 = markdown_element_list[i]
                text_2 = markdown_element_list[i + 1]
                if ("<table>" in text_1 or "<tr>" in text_1) and ("<table>" in text_2 or "<tr>" in text_2):
                    query = build_html_table_merge_query(text_1, text_2)
                    html_table_merge_query_list.append(query)
                    html_table_merge_query_keys.append((page_num, i, i + 1))
        
        if html_table_merge_query_list:
            responses = llm.generate(html_table_merge_query_list, sampling_params=sampling_params)
            results = [response.outputs[0].text for response in responses]
            
            for key, result in zip(html_table_merge_query_keys, results):
                try:
                    if not result.startswith("ERROR:"):
                        html_table_merge_result[key] = result.strip()
                except:
                    pass
        
        # 构建最终文档
        print("🔄 构建最终文档...")
        document_text = bulid_document_text(page_to_markdown_result, element_merge_detect_result, html_table_merge_result)
        
        print("✅ 文档解析完成!")
        return {
            'document_text': document_text,
            'page_to_markdown_result': page_to_markdown_result,
            'element_merge_detect_result': element_merge_detect_result,
            'html_table_merge_result': html_table_merge_result
        }
        
    except Exception as e:
        print(f"❌ 解析过程中出现错误: {e}")
        return None


if __name__ == '__main__':
    # 测试代码
    from transformer_hf import TransformersLLMWrapper
    
    file_path = 'test.pdf'
    llm = TransformersLLMWrapper(
        model_path="ChatDOC/OCRFlux-3B",
        gpu_memory_utilization=0.8,
        max_model_len=8192
    )
    
    result = parse(llm, file_path)
    if result is not None:
        document_markdown = result['document_text']
        print(document_markdown)
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
    else:
        print("解析失败")
