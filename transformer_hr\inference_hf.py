"""
Inference module using Hugging Face Transformers framework
Replaces vLLM-related functionality in original inference.py while maintaining interface compatibility
"""

import json
import copy
from PIL import Image
from pypdf import PdfReader
from ocrflux.image_utils import get_page_image
from ocrflux.table_format import table_matrix2html
from ocrflux.prompts import PageResponse, build_page_to_markdown_prompt, build_element_merge_detect_prompt, build_html_table_merge_prompt


class SamplingParams:
    """
    Sampling parameters class, compatible with vLLM's SamplingParams interface
    """
    def __init__(self, temperature=0.0, max_tokens=8192):
        self.temperature = temperature
        self.max_tokens = max_tokens


def build_qwen2_5_vl_prompt(question):
    """
    Build Qwen2.5-VL format prompt

    Args:
        question: Question text

    Returns:
        str: Formatted prompt (now directly returns original question, let processor handle formatting)
    """
    # Directly return question text, let processor's apply_chat_template handle formatting
    return question


def build_page_to_markdown_query(file_path: str, page_number: int, target_longest_image_dim: int = 1024, image_rotation: int = 0) -> dict:
    """
    Build page to Markdown query

    Args:
        file_path: File path
        page_number: Page number
        target_longest_image_dim: Target longest image dimension
        image_rotation: Image rotation angle

    Returns:
        dict: Query dictionary containing prompt and multi_modal_data
    """
    assert image_rotation in [0, 90, 180, 270], "Invalid image rotation provided in build_page_query"
    image = get_page_image(file_path, page_number, target_longest_image_dim=target_longest_image_dim, image_rotation=image_rotation)
    question = build_page_to_markdown_prompt()
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def build_element_merge_detect_query(text_list_1, text_list_2) -> dict:
    """
    Build element merge detection query

    Args:
        text_list_1: Text list 1
        text_list_2: Text list 2

    Returns:
        dict: Query dictionary
    """
    image = Image.new('RGB', (28, 28), color='black')
    question = build_element_merge_detect_prompt(text_list_1, text_list_2)
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def build_html_table_merge_query(text_1, text_2) -> dict:
    """
    Build HTML table merge query

    Args:
        text_1: Text 1
        text_2: Text 2

    Returns:
        dict: Query dictionary
    """
    image = Image.new('RGB', (28, 28), color='black')
    question = build_html_table_merge_prompt(text_1, text_2)
    prompt = build_qwen2_5_vl_prompt(question)
    query = {
        "prompt": prompt,
        "multi_modal_data": {"image": image},
    }
    return query


def bulid_document_text(page_to_markdown_result, element_merge_detect_result, html_table_merge_result):
    """
    Build document text

    Args:
        page_to_markdown_result: Page to Markdown results
        element_merge_detect_result: Element merge detection results
        html_table_merge_result: HTML table merge results

    Returns:
        str: Built document text
    """
    page_to_markdown_keys = list(page_to_markdown_result.keys())
    page_to_markdown_keys.sort()
    
    document_text = ""
    for page_num in page_to_markdown_keys:
        markdown_element_list = page_to_markdown_result[page_num]
        
        # Handle cross-page merging
        if page_num > 1 and element_merge_detect_result.get((page_num-1, page_num)):
            # Merge logic
            pass
        
        # Handle HTML table merging
        merged_elements = []
        i = 0
        while i < len(markdown_element_list):
            current_element = markdown_element_list[i]
            if i < len(markdown_element_list) - 1:
                next_element = markdown_element_list[i + 1]
                merge_key = (page_num, i, i + 1)
                if merge_key in html_table_merge_result:
                    merged_elements.append(html_table_merge_result[merge_key])
                    i += 2  # Skip next element
                    continue
            merged_elements.append(current_element)
            i += 1
        
        # Add to document text
        for element in merged_elements:
            if element.strip():
                document_text += element + "\n\n"
    
    return document_text.strip()


def parse(llm, file_path, skip_cross_page_merge=False, max_page_retries=0):
    """
    Parse document, compatible with vLLM and Transformers frameworks

    Args:
        llm: Language model instance (supports generate method)
        file_path: File path
        skip_cross_page_merge: Whether to skip cross-page merging
        max_page_retries: Maximum page retry count

    Returns:
        dict: Parse results containing document_text field
    """
    print(f"🔍 Starting document parsing: {file_path}")
    
    # Create sampling parameters
    sampling_params = SamplingParams(temperature=0.0, max_tokens=8192)
    
    # Determine number of pages
    if file_path.lower().endswith(".pdf"):
        try:
            reader = PdfReader(file_path)
            num_pages = reader.get_num_pages()
            print(f"📄 PDF document, {num_pages} pages total")
        except Exception as e:
            print(f"❌ Failed to read PDF: {e}")
            return None
    else:
        num_pages = 1
        print(f"🖼️ Image document, 1 page")
    
    try:
        # Stage 1: Page to Markdown
        print("🔄 Stage 1: Page to Markdown...")
        page_to_markdown_query_list = [build_page_to_markdown_query(file_path, page_num) for page_num in range(1, num_pages + 1)]
        
        print(f"📝 Generated {len(page_to_markdown_query_list)} queries")
        responses = llm.generate(page_to_markdown_query_list, sampling_params=sampling_params)
        results = [response.outputs[0].text for response in responses]
        
        page_to_markdown_result = {}
        retry_list = []
        
        for i, result in enumerate(results):
            try:
                # Check if it's an error response
                if result.startswith("ERROR:"):
                    print(f"⚠️ Page {i+1} processing error: {result}")
                    retry_list.append(i)
                    continue
                
                json_data = json.loads(result)
                page_response = PageResponse(**json_data)
                natural_text = page_response.natural_text
                markdown_element_list = []
                
                for text in natural_text.split('\n\n'):
                    if text.startswith("<Image>") and text.endswith("</Image>"):
                        pass  # Skip image markers
                    elif text.startswith("<table>") and text.endswith("</table>"):
                        try:
                            new_text = table_matrix2html(text)
                        except:
                            new_text = text.replace("<t>", "").replace("<l>", "").replace("<lt>", "")
                        markdown_element_list.append(new_text)
                    else:
                        markdown_element_list.append(text)
                
                page_to_markdown_result[i+1] = markdown_element_list
                print(f"✅ Page {i+1} processed successfully")
                
            except Exception as e:
                print(f"⚠️ Page {i+1} JSON parsing failed: {e}")
                retry_list.append(i)
        
        # Retry failed pages
        attempt = 0
        while len(retry_list) > 0 and attempt < max_page_retries:
            print(f"🔄 Retry attempt {attempt+1}, {len(retry_list)} pages remaining")
            retry_page_to_markdown_query_list = [build_page_to_markdown_query(file_path, page_num+1) for page_num in retry_list]
            retry_sampling_params = SamplingParams(temperature=0.1*attempt, max_tokens=8192)
            responses = llm.generate(retry_page_to_markdown_query_list, sampling_params=retry_sampling_params)
            results = [response.outputs[0].text for response in responses]
            next_retry_list = []
            
            for i, result in zip(retry_list, results):
                try:
                    if result.startswith("ERROR:"):
                        next_retry_list.append(i)
                        continue
                    
                    json_data = json.loads(result)
                    page_response = PageResponse(**json_data)
                    natural_text = page_response.natural_text
                    markdown_element_list = []
                    
                    for text in natural_text.split('\n\n'):
                        if text.startswith("<Image>") and text.endswith("</Image>"):
                            pass
                        elif text.startswith("<table>") and text.endswith("</table>"):
                            try:
                                new_text = table_matrix2html(text)
                            except:
                                new_text = text.replace("<t>", "").replace("<l>", "").replace("<lt>", "")
                            markdown_element_list.append(new_text)
                        else:
                            markdown_element_list.append(text)
                    
                    page_to_markdown_result[i+1] = markdown_element_list
                    print(f"✅ Retry page {i+1} successful")
                    
                except Exception as e:
                    print(f"⚠️ Retry page {i+1} still failed: {e}")
                    next_retry_list.append(i)
            
            retry_list = next_retry_list
            attempt += 1
        
        if len(retry_list) > 0:
            print(f"⚠️ {len(retry_list)} pages finally failed to process")
        
        # Stage 2: Element Merge Detection (if not skipping cross-page merging)
        element_merge_detect_result = {}
        if not skip_cross_page_merge and num_pages > 1:
            print("🔄 Stage 2: Element merge detection...")
            element_merge_detect_query_list = []
            element_merge_detect_query_page_pairs = []
            
            for page_num in range(1, num_pages):
                if page_num in page_to_markdown_result and (page_num + 1) in page_to_markdown_result:
                    text_list_1 = page_to_markdown_result[page_num]
                    text_list_2 = page_to_markdown_result[page_num + 1]
                    if len(text_list_1) > 0 and len(text_list_2) > 0:
                        query = build_element_merge_detect_query(text_list_1, text_list_2)
                        element_merge_detect_query_list.append(query)
                        element_merge_detect_query_page_pairs.append((page_num, page_num + 1))
            
            if element_merge_detect_query_list:
                responses = llm.generate(element_merge_detect_query_list, sampling_params=sampling_params)
                results = [response.outputs[0].text for response in responses]
                
                for (page_num_1, page_num_2), result in zip(element_merge_detect_query_page_pairs, results):
                    try:
                        if not result.startswith("ERROR:"):
                            element_merge_detect_result[(page_num_1, page_num_2)] = result.strip().lower() == "yes"
                    except:
                        element_merge_detect_result[(page_num_1, page_num_2)] = False
        
        # Stage 3: HTML Table Merge
        print("🔄 Stage 3: HTML table merging...")
        html_table_merge_result = {}
        html_table_merge_query_list = []
        html_table_merge_query_keys = []
        
        for page_num in page_to_markdown_result:
            markdown_element_list = page_to_markdown_result[page_num]
            for i in range(len(markdown_element_list) - 1):
                text_1 = markdown_element_list[i]
                text_2 = markdown_element_list[i + 1]
                if ("<table>" in text_1 or "<tr>" in text_1) and ("<table>" in text_2 or "<tr>" in text_2):
                    query = build_html_table_merge_query(text_1, text_2)
                    html_table_merge_query_list.append(query)
                    html_table_merge_query_keys.append((page_num, i, i + 1))
        
        if html_table_merge_query_list:
            responses = llm.generate(html_table_merge_query_list, sampling_params=sampling_params)
            results = [response.outputs[0].text for response in responses]
            
            for key, result in zip(html_table_merge_query_keys, results):
                try:
                    if not result.startswith("ERROR:"):
                        html_table_merge_result[key] = result.strip()
                except:
                    pass
        
        # Build final document
        print("🔄 Building final document...")
        document_text = bulid_document_text(page_to_markdown_result, element_merge_detect_result, html_table_merge_result)
        
        print("✅ Document parsing completed!")
        return {
            'document_text': document_text,
            'page_to_markdown_result': page_to_markdown_result,
            'element_merge_detect_result': element_merge_detect_result,
            'html_table_merge_result': html_table_merge_result
        }
        
    except Exception as e:
        print(f"❌ Error occurred during parsing: {e}")
        return None


if __name__ == '__main__':
    # Test code
    from transformer_hf import TransformersLLMWrapper
    
    file_path = 'test.pdf'
    llm = TransformersLLMWrapper(
        model_path="/root/.cache/huggingface/hub/models--ChatDOC--OCRFlux-3B",
        npu_memory_utilization=0.8,
        max_model_len=8192,
        local_files_only=True
    )
    
    result = parse(llm, file_path)
    if result is not None:
        document_markdown = result['document_text']
        print(document_markdown)
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
    else:
        print("Parsing failed")
