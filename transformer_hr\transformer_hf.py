"""
OCR processing script using Hugging Face Transformers framework to replace vLLM
Maintains the same functionality as the original transformer.py but implemented with Transformers library
"""

import torch
import gc
import torch_npu
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    AutoProcessor,
    AutoConfig,
    AutoModel
)

# Try to import various Qwen model classes (optional)
QWEN_MODELS = []

try:
    from transformers import Qwen2VLForConditionalGeneration
    QWEN_MODELS.append(("Qwen2VL", Qwen2VLForConditionalGeneration))
    print("✅ Qwen2VLForConditionalGeneration available")
except ImportError:
    print("⚠️ Qwen2VLForConditionalGeneration not available")

from inference_hf import parse

def create_optimized_model(model_path="ChatDOC/OCRFlux-3B"):
    """
    Create optimized Transformers model for NPU, equivalent to vLLM's memory_utilization=0.8 and max_model_len=8192

    Args:
        model_path: Model path

    Returns:
        tuple: (model, processor) Model and processor
    """
    # Clear NPU memory
    if torch.npu.is_available():
        torch.npu.empty_cache()
    gc.collect()
    
    # First check model configuration to determine the correct model class
    try:
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
        model_type = config.model_type.lower() if hasattr(config, 'model_type') else "unknown"
        print(f"🔍 Detected model type: {model_type}")
    except Exception as e:
        print(f"⚠️ Unable to read model configuration: {e}")
        model_type = "unknown"
    
    # Select appropriate model class based on detected model type
    # Prioritize model classes that support generation
    model_classes = []
    
    # Highest priority: Try AutoModelForImageTextToText (used in original code)
    try:
        from transformers import AutoModelForImageTextToText
        model_classes.append(("AutoImageTextToText", AutoModelForImageTextToText))
        print("✅ AutoModelForImageTextToText available")
    except ImportError:
        print("⚠️ AutoModelForImageTextToText not available")
    
    # Second priority: Successfully imported Qwen model classes
    model_classes.extend(QWEN_MODELS)
    
    # Third priority: Other model classes that support generation
    model_classes.append(("AutoCausal", AutoModelForCausalLM))
    
    # Last resort: Use AutoModel (may not support generation)
    model_classes.append(("AutoModel", AutoModel))
    
    print(f"📋 Will try the following model classes: {[name for name, _ in model_classes]}")
    
    model = None
    for class_name, model_class in model_classes:
        try:
            print(f"🔄 Trying to load model with {class_name}...")
            # Load model directly for NPU
            model = model_class.from_pretrained(
                model_path,
                torch_dtype=torch.float16,  # Use float16 to reduce NPU memory
                device_map=None,  # Manual device allocation for NPU
                low_cpu_mem_usage=True,  # Reduce CPU memory usage
                trust_remote_code=True,  # Trust remote code
                attn_implementation=None  # Use default attention mechanism
            )

            # Move model to NPU if available
            if torch.npu.is_available():
                model = model.to('npu:0')
                print(f"✅ Model moved to NPU device: npu:0")
            print(f"✅ Successfully loaded model with {class_name}")
            break
        except Exception as e:
            print(f"❌ {class_name} loading failed: {e}")
            continue
    
    if model is None:
        raise RuntimeError("All model classes failed to load, please check model path and dependencies")
    
    # Set model to evaluation mode (disable dropout and other training features)
    model.eval()
    
    # Enable gradient checkpointing to save NPU memory (if supported)
    if hasattr(model, 'gradient_checkpointing_enable'):
        model.gradient_checkpointing_enable()
        print("✅ Gradient checkpointing enabled")
    
    # Load processor
    try:
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        print("✅ Successfully loaded AutoProcessor")
    except Exception as e:
        print(f"⚠️ AutoProcessor loading failed: {e}")
        try:
            # Try using tokenizer
            processor = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            print("✅ Successfully loaded AutoTokenizer as processor")
        except Exception as e2:
            print(f"❌ Processor loading failed: {e2}")
            raise RuntimeError("Unable to load processor")
    
    print(f"🚀 Model loading completed, device: {model.device}")
    if torch.npu.is_available():
        print(f"💾 NPU memory usage: {torch.npu.memory_allocated() / 1024**3:.2f} GB")
    
    return model, processor


class TransformersLLMWrapper:
    """
    Transformers model wrapper that provides vLLM-compatible interface
    Equivalent to vLLM's LLM class functionality
    """
    
    def __init__(self, model_path="ChatDOC/OCRFlux-3B", npu_memory_utilization=0.8, max_model_len=8192):
        """
        Initialize model wrapper

        Args:
            model_path: Model path
            npu_memory_utilization: NPU memory utilization rate (for memory optimization configuration)
            max_model_len: Maximum model length (for generation parameter configuration)
        """
        self.model_path = model_path
        self.max_model_len = max_model_len
        self.npu_memory_utilization = npu_memory_utilization

        # Create optimized model
        self.model, self.processor = create_optimized_model(model_path)

        print(f"🎯 TransformersLLMWrapper initialization completed")
        print(f"   - Model path: {model_path}")
        print(f"   - NPU memory utilization: {npu_memory_utilization}")
        print(f"   - Maximum length: {max_model_len}")
    
    def generate(self, query_list, sampling_params=None):
        """
        Batch text generation, compatible with vLLM's generate interface

        Args:
            query_list: Query list, each element contains prompt and multi_modal_data
            sampling_params: Sampling parameter object

        Returns:
            list: Response object list, each object contains outputs attribute
        """
        # Parse sampling parameters
        temperature = getattr(sampling_params, 'temperature', 0.0) if sampling_params else 0.0
        max_tokens = getattr(sampling_params, 'max_tokens', 4096) if sampling_params else 4096
        
        # Limit maximum token count to avoid NPU memory overflow
        max_tokens = min(max_tokens, self.max_model_len // 2)  # Conservative estimate

        responses = []

        try:
            with torch.no_grad():  # Reduce NPU memory usage
                for i, query in enumerate(query_list):
                    try:
                        # Extract prompt and image
                        prompt = query["prompt"]
                        image = query["multi_modal_data"]["image"]
                        
                        # Use correct processing method for Qwen2.5-VL (based on original transformer.py)
                        try:
                            # Build correct messages format
                            messages = [
                                {"role": "system", "content": "You are a helpful assistant."},
                                {"role": "user", "content": [
                                    {"type": "image", "image": image},
                                    {"type": "text", "text": prompt},
                                ]},
                            ]
                            
                            # Use apply_chat_template to process messages
                            text = self.processor.apply_chat_template(
                                messages, 
                                tokenize=False, 
                                add_generation_prompt=True
                            )
                            
                            # Call processor with correct parameters
                            inputs = self.processor(
                                text=[text],
                                images=[image],
                                padding=True,
                                return_tensors="pt"
                            )

                            # Move inputs to NPU if available
                            if torch.npu.is_available():
                                inputs = {k: v.to('npu:0') if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}
                            
                            print(f"✅ Successfully processed query {i+1}: text length={len(text)}, image size={image.size}")
                            
                        except Exception as e:
                            print(f"❌ Qwen2.5-VL processing failed: {e}")
                            print(f"⚠️ Trying fallback solution...")
                            
                            # Fallback solution: process text only
                            try:
                                inputs = self.processor(
                                    prompt,
                                    return_tensors="pt",
                                    padding=True,
                                    truncation=True,
                                    max_length=self.max_model_len
                                )

                                # Move inputs to NPU if available
                                if torch.npu.is_available():
                                    inputs = {k: v.to('npu:0') if isinstance(v, torch.Tensor) else v for k, v in inputs.items()}
                                print(f"✅ Fallback solution successful (text only)")
                            except Exception as e2:
                                print(f"❌ Fallback solution also failed: {e2}")
                                raise e2
                        
                        # Generate text, adapt to different model types
                        generation_kwargs = {
                            **inputs,
                            "max_new_tokens": max_tokens,
                            "do_sample": temperature > 0,  # Use greedy decoding when temperature is 0
                            "use_cache": True,  # Use cache to improve efficiency
                            "num_beams": 1,  # Use greedy search
                        }
                        
                        # Add temperature parameter (if supported)
                        if temperature > 0:
                            generation_kwargs["temperature"] = temperature
                        
                        # Set pad_token_id
                        if hasattr(self.processor, 'tokenizer') and hasattr(self.processor.tokenizer, 'eos_token_id'):
                            generation_kwargs["pad_token_id"] = self.processor.tokenizer.eos_token_id
                        elif hasattr(self.processor, 'eos_token_id'):
                            generation_kwargs["pad_token_id"] = self.processor.eos_token_id
                        
                        output_ids = self.model.generate(**generation_kwargs)
                        
                        # Extract generated tokens using the same method as original transformer.py
                        generated_ids = [
                            output_ids[len(input_ids):] 
                            for input_ids, output_ids in zip(inputs.input_ids, output_ids)
                        ]
                        
                        # Decode text
                        generated_text = self.processor.batch_decode(
                            generated_ids, 
                            skip_special_tokens=True, 
                            clean_up_tokenization_spaces=True
                        )[0]
                        
                        # Create vLLM-compatible response object
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response(generated_text))
                        
                        # Clean up temporary variables
                        del inputs, output_ids, generated_ids
                        
                        # Periodically clean NPU memory
                        if (i + 1) % 5 == 0:  # Clean every 5 queries
                            if torch.npu.is_available():
                                torch.npu.empty_cache()
                            gc.collect()

                        print(f"✅ Processing completed {i+1}/{len(query_list)}")

                    except RuntimeError as e:
                        if "out of memory" in str(e).lower() or "npu" in str(e).lower():
                            print(f"❌ NPU out of memory error (query {i+1}): {e}")
                            print("🔧 Trying to clean NPU memory and retry...")
                            if torch.npu.is_available():
                                torch.npu.empty_cache()
                            gc.collect()
                        else:
                            raise e
                        
                        # Create error response
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response("ERROR: NPU out of memory"))
                        
                    except Exception as e:
                        print(f"❌ Processing error (query {i+1}): {e}")
                        
                        # Create error response
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response(f"ERROR: {str(e)}"))
                
        except Exception as e:
            print(f"❌ Batch generation error: {e}")
            
        finally:
            # Final cleanup
            if torch.npu.is_available():
                torch.npu.empty_cache()
            gc.collect()
        
        return responses


def main():
    """
    Main function, equivalent to the functionality of original transformer.py
    """
    # File path configuration
    file_path = 'avce-20-2023.pdf'
    # file_path = 'test.png'
    
    print("🚀 Starting OCR processing using Transformers framework...")
    
    # Create LLM instance (equivalent to vLLM's LLM class)
    llm = TransformersLLMWrapper(
        model_path="ChatDOC/OCRFlux-3B",
        npu_memory_utilization=0.8,  # Corresponds to quantization and NPU memory optimization configuration
        max_model_len=8192  # Corresponds to maximum sequence length
    )
    
    # Call parse function for processing
    result = parse(llm, file_path)
    
    if result is not None:
        document_markdown = result['document_text']
        print("✅ OCR processing completed!")
        print(document_markdown)

        # Save results to file
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
        print("💾 Results saved to test.md")
    else:
        print("❌ OCR processing failed")


if __name__ == "__main__":
    main()
