"""
使用Hugging Face Transformers框架替代vLLM的OCR处理脚本
保持与原始transformer.py相同的功能，但使用Transformers库实现
"""

import torch
import gc
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer, 
    AutoProcessor, 
    AutoConfig,
    AutoModel
)

# 尝试导入各种Qwen模型类（可选）
QWEN_MODELS = []

try:
    from transformers import Qwen2VLForConditionalGeneration
    QWEN_MODELS.append(("Qwen2VL", Qwen2VLForConditionalGeneration))
    print("✅ Qwen2VLForConditionalGeneration 可用")
except ImportError:
    print("⚠️ Qwen2VLForConditionalGeneration 不可用")

from inference_hf import parse

def create_optimized_model(model_path="ChatDOC/OCRFlux-3B"):
    """
    创建优化的Transformers模型，等效于vLLM的gpu_memory_utilization=0.8和max_model_len=8192
    
    Args:
        model_path: 模型路径
        
    Returns:
        tuple: (model, processor) 模型和处理器
    """
    # 清理显存
    torch.cuda.empty_cache()
    gc.collect()
    
    # 首先检查模型配置以确定正确的模型类
    try:
        config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)
        model_type = config.model_type.lower() if hasattr(config, 'model_type') else "unknown"
        print(f"🔍 检测到模型类型: {model_type}")
    except Exception as e:
        print(f"⚠️ 无法读取模型配置: {e}")
        model_type = "unknown"
    
    # 根据检测到的模型类型选择合适的模型类
    # 优先使用支持生成的模型类
    model_classes = []
    
    # 最高优先级：尝试AutoModelForImageTextToText（原始代码使用的）
    try:
        from transformers import AutoModelForImageTextToText
        model_classes.append(("AutoImageTextToText", AutoModelForImageTextToText))
        print("✅ AutoModelForImageTextToText 可用")
    except ImportError:
        print("⚠️ AutoModelForImageTextToText 不可用")
    
    # 次优先级：已成功导入的Qwen模型类
    model_classes.extend(QWEN_MODELS)
    
    # 第三优先级：其他支持生成的模型类
    model_classes.append(("AutoCausal", AutoModelForCausalLM))
    
    # 最后才使用AutoModel（可能不支持生成）
    model_classes.append(("AutoModel", AutoModel))
    
    print(f"📋 将尝试以下模型类: {[name for name, _ in model_classes]}")
    
    model = None
    for class_name, model_class in model_classes:
        try:
            print(f"🔄 尝试使用 {class_name} 加载模型...")
            # 直接加载模型（无量化）
            model = model_class.from_pretrained(
                model_path,
                torch_dtype=torch.float16,  # 使用float16减少显存
                device_map="auto",  # 自动分配设备（GPU/CPU）
                low_cpu_mem_usage=True,  # 减少CPU内存使用
                trust_remote_code=True,  # 信任远程代码
                attn_implementation=None  # 使用默认注意力机制
            )
            print(f"✅ 成功使用 {class_name} 加载模型")
            break
        except Exception as e:
            print(f"❌ {class_name} 加载失败: {e}")
            continue
    
    if model is None:
        raise RuntimeError("所有模型类都加载失败，请检查模型路径和依赖")
    
    # 设置模型为评估模式（关闭dropout等训练特性）
    model.eval()
    
    # 启用梯度检查点以节省显存（如果支持）
    if hasattr(model, 'gradient_checkpointing_enable'):
        model.gradient_checkpointing_enable()
        print("✅ 启用梯度检查点")
    
    # 加载处理器
    try:
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        print("✅ 成功加载AutoProcessor")
    except Exception as e:
        print(f"⚠️ AutoProcessor加载失败: {e}")
        try:
            # 尝试使用分词器
            processor = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            print("✅ 成功加载AutoTokenizer作为处理器")
        except Exception as e2:
            print(f"❌ 处理器加载失败: {e2}")
            raise RuntimeError("无法加载处理器")
    
    print(f"🚀 模型加载完成，设备: {model.device}")
    if torch.cuda.is_available():
        print(f"💾 显存使用: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    
    return model, processor


class TransformersLLMWrapper:
    """
    Transformers模型包装器，提供与vLLM兼容的接口
    等效于vLLM的LLM类功能
    """
    
    def __init__(self, model_path="ChatDOC/OCRFlux-3B", gpu_memory_utilization=0.8, max_model_len=8192):
        """
        初始化模型包装器
        
        Args:
            model_path: 模型路径
            gpu_memory_utilization: GPU显存使用率（用于显存优化配置）
            max_model_len: 最大模型长度（用于生成参数配置）
        """
        self.model_path = model_path
        self.max_model_len = max_model_len
        self.gpu_memory_utilization = gpu_memory_utilization
        
        # 创建优化的模型
        self.model, self.processor = create_optimized_model(model_path)
        
        print(f"🎯 TransformersLLMWrapper 初始化完成")
        print(f"   - 模型路径: {model_path}")
        print(f"   - 显存使用率: {gpu_memory_utilization}")
        print(f"   - 最大长度: {max_model_len}")
    
    def generate(self, query_list, sampling_params=None):
        """
        批量生成文本，兼容vLLM的generate接口
        
        Args:
            query_list: 查询列表，每个元素包含prompt和multi_modal_data
            sampling_params: 采样参数对象
            
        Returns:
            list: 响应对象列表，每个对象包含outputs属性
        """
        # 解析采样参数
        temperature = getattr(sampling_params, 'temperature', 0.0) if sampling_params else 0.0
        max_tokens = getattr(sampling_params, 'max_tokens', 4096) if sampling_params else 4096
        
        # 限制最大token数以避免显存溢出
        max_tokens = min(max_tokens, self.max_model_len // 2)  # 保守估计
        
        responses = []
        
        try:
            with torch.no_grad():  # 减少显存使用
                for i, query in enumerate(query_list):
                    try:
                        # 提取prompt和图像
                        prompt = query["prompt"]
                        image = query["multi_modal_data"]["image"]
                        
                        # 使用Qwen2.5-VL的正确处理方式（基于原始transformer.py）
                        try:
                            # 构建正确的messages格式
                            messages = [
                                {"role": "system", "content": "You are a helpful assistant."},
                                {"role": "user", "content": [
                                    {"type": "image", "image": image},
                                    {"type": "text", "text": prompt},
                                ]},
                            ]
                            
                            # 使用apply_chat_template处理messages
                            text = self.processor.apply_chat_template(
                                messages, 
                                tokenize=False, 
                                add_generation_prompt=True
                            )
                            
                            # 使用正确的参数调用processor
                            inputs = self.processor(
                                text=[text], 
                                images=[image], 
                                padding=True, 
                                return_tensors="pt"
                            ).to(self.model.device)
                            
                            print(f"✅ 成功处理查询 {i+1}: 文本长度={len(text)}, 图像尺寸={image.size}")
                            
                        except Exception as e:
                            print(f"❌ Qwen2.5-VL处理失败: {e}")
                            print(f"⚠️ 尝试使用备用方案...")
                            
                            # 备用方案：只处理文本
                            try:
                                inputs = self.processor(
                                    prompt,
                                    return_tensors="pt",
                                    padding=True,
                                    truncation=True,
                                    max_length=self.max_model_len
                                ).to(self.model.device)
                                print(f"✅ 备用方案成功（仅文本）")
                            except Exception as e2:
                                print(f"❌ 备用方案也失败: {e2}")
                                raise e2
                        
                        # 生成文本，适配不同的模型类型
                        generation_kwargs = {
                            **inputs,
                            "max_new_tokens": max_tokens,
                            "do_sample": temperature > 0,  # 温度为0时使用贪婪解码
                            "use_cache": True,  # 使用缓存提高效率
                            "num_beams": 1,  # 使用贪婪搜索
                        }
                        
                        # 添加温度参数（如果支持）
                        if temperature > 0:
                            generation_kwargs["temperature"] = temperature
                        
                        # 设置pad_token_id
                        if hasattr(self.processor, 'tokenizer') and hasattr(self.processor.tokenizer, 'eos_token_id'):
                            generation_kwargs["pad_token_id"] = self.processor.tokenizer.eos_token_id
                        elif hasattr(self.processor, 'eos_token_id'):
                            generation_kwargs["pad_token_id"] = self.processor.eos_token_id
                        
                        output_ids = self.model.generate(**generation_kwargs)
                        
                        # 使用与原始transformer.py相同的方式提取生成的token
                        generated_ids = [
                            output_ids[len(input_ids):] 
                            for input_ids, output_ids in zip(inputs.input_ids, output_ids)
                        ]
                        
                        # 解码文本
                        generated_text = self.processor.batch_decode(
                            generated_ids, 
                            skip_special_tokens=True, 
                            clean_up_tokenization_spaces=True
                        )[0]
                        
                        # 创建兼容vLLM的响应对象
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response(generated_text))
                        
                        # 清理临时变量
                        del inputs, output_ids, generated_ids
                        
                        # 定期清理显存
                        if (i + 1) % 5 == 0:  # 每5个查询清理一次
                            torch.cuda.empty_cache()
                            gc.collect()
                        
                        print(f"✅ 处理完成 {i+1}/{len(query_list)}")
                        
                    except torch.cuda.OutOfMemoryError as e:
                        print(f"❌ 显存不足错误 (查询 {i+1}): {e}")
                        print("🔧 尝试清理显存并重试...")
                        torch.cuda.empty_cache()
                        gc.collect()
                        
                        # 创建错误响应
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response("ERROR: 显存不足"))
                        
                    except Exception as e:
                        print(f"❌ 处理错误 (查询 {i+1}): {e}")
                        
                        # 创建错误响应
                        class Output:
                            def __init__(self, text):
                                self.text = text
                        
                        class Response:
                            def __init__(self, text):
                                self.outputs = [Output(text)]
                        
                        responses.append(Response(f"ERROR: {str(e)}"))
                
        except Exception as e:
            print(f"❌ 批量生成错误: {e}")
            
        finally:
            # 最终清理
            torch.cuda.empty_cache()
            gc.collect()
        
        return responses


def main():
    """
    主函数，等效于原始transformer.py的功能
    """
    # 文件路径配置
    file_path = 'avce-20-2023.pdf'
    # file_path = 'test.png'
    
    print("🚀 开始使用Transformers框架进行OCR处理...")
    
    # 创建LLM实例（等效于vLLM的LLM类）
    llm = TransformersLLMWrapper(
        model_path="ChatDOC/OCRFlux-3B",
        gpu_memory_utilization=0.8,  # 对应量化和显存优化配置
        max_model_len=8192  # 对应最大序列长度
    )
    
    # 调用parse函数进行处理
    result = parse(llm, file_path)
    
    if result is not None:
        document_markdown = result['document_text']
        print("✅ OCR处理完成!")
        print(document_markdown)
        
        # 保存结果到文件
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
        print("💾 结果已保存到 test.md")
    else:
        print("❌ OCR处理失败")


if __name__ == "__main__":
    main()
