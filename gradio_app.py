# Copyright (c) Opendatalab. All rights reserved.
import gradio as gr
import os
import tempfile
from vllm import LLM
from ocrflux.inference import parse
import time
from datetime import datetime
import traceback
from PIL import Image
import base64
import sys
import logging
# 配置日志格式和级别，确保在终端输出
logging.basicConfig(level=logging.INFO,
                    format='[%(asctime)s] %(levelname)s: %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

# 添加hehe目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'hehe'))

# 导入合合OCR客户端
try:
    from ocr_client import OCRClient
    HEHEOCR_SUPPORT = True
except ImportError:
    print("警告: 无法导入合合OCR客户端")
    HEHEOCR_SUPPORT = False

# 尝试导入PDF处理库
try:
    import PyMuPDF as fitz
    PDF_SUPPORT = True
except ImportError:
    try:
        import fitz
        PDF_SUPPORT = True
    except ImportError:
        print("警告: 未找到PyMuPDF库，PDF预览功能将被禁用")
        print("安装方法: pip install PyMuPDF")
        PDF_SUPPORT = False

# 全局变量存储LLM实例
global_llm = None
global_model_name = None

# 合合OCR配置
HEHEOCR_API_BASE_URL = "http://**************:10070"

def initialize_model(model_name, gpu_memory, max_model_len):
    """初始化或重新加载模型"""
    global global_llm, global_model_name

    if model_name == "ChatDOC/OCRFlux-3B":
        _model_name = "OCRFlux"
    else:
        _model_name = "合合OCR"
    
    try:
        # 如果模型已存在且名称相同，直接返回
        if global_llm is not None and global_model_name == model_name:
            return "✅ 模型已就绪", True
        
        # 释放旧模型
        if global_llm is not None:
            del global_llm
            global_llm = None
        
        # 初始化新模型
        global_llm = LLM(
            model=model_name,
            gpu_memory_utilization=gpu_memory,
            max_model_len=max_model_len,
            tensor_parallel_size=1
        )
        global_model_name = model_name
        
        return f"模型 {_model_name} 加载成功", True

    except Exception as e:
        error_msg = f"❌ 模型加载失败: {str(e)}"
        return error_msg, False

def process_file_with_heheocr(file_path):
    """使用合合OCR处理文件"""
    if not HEHEOCR_SUPPORT:
        return "❌ 合合OCR客户端不可用", None

    try:
        # 创建OCR客户端
        logging.info(f"[HeheOCR] 创建OCR客户端，准备处理文件: {file_path}")
        client = OCRClient(HEHEOCR_API_BASE_URL)

        # 处理文档并获取markdown
        result_data = client.process_document_with_markdown(file_path)

        if result_data and result_data["result"].get("code") == 200:
            # 提取markdown内容
            if result_data["markdown_content"]:
                # 将bytes转换为字符串
                markdown_content = result_data["markdown_content"].decode('utf-8')
                logging.info("[HeheOCR] 文档处理成功，已获取 Markdown 内容")
                logging.info("[HeheOCR] 返回 Markdown 结果至前端")
                return "✅ 合合OCR处理成功", markdown_content
            else:
                # 如果没有markdown，提取文本内容
                data = result_data["result"].get("data", {})
                parse_result = data.get("parse_result", {})
                detail = parse_result.get("detail", [])

                text_content = []
                for item in detail:
                    if item.get("type") == "paragraph":
                        text_content.append(item.get("text", ""))

                if text_content:
                    logging.info("[HeheOCR] 返回纯文本结果至前端")
                    return "✅ 合合OCR处理成功", "\n".join(text_content)
                else:
                    return "❌ 合合OCR处理失败：无法提取内容", None
        else:
            logging.error("[HeheOCR] 处理失败，API返回非成功状态")
            return "❌ 合合OCR处理失败", None

    except Exception as e:
        error_msg = f"❌ 合合OCR处理失败: {str(e)}"
        logging.error(error_msg)
        return error_msg, None

def process_file(file, model_name, gpu_memory, max_model_len, target_dim, max_retries, skip_merge):
    """处理上传的文件"""
    if file is None:
        return "⚠️ 请先上传文件", None

    if model_name == "ChatDOC/OCRFlux-3B":
        _model_name = "OCRFlux"
    else:
        _model_name = "合合OCR"

    try:
        # 文件验证
        file_ext = os.path.splitext(file.name)[1].lower()
        if file_ext not in ['.pdf', '.png', '.jpg', '.jpeg']:
            return "❌ 不支持的文件格式，请上传 PDF 或图片文件", None

        # 文件大小检查（限制为50MB）
        file_size = os.path.getsize(file.name) / (1024 * 1024)  # MB
        if file_size > 50:
            return f"❌ 文件过大 ({file_size:.1f}MB)，请上传小于50MB的文件", None

        start_time = time.time()

        # 显示处理状态
        status = f"📄 处理文件: {os.path.basename(file.name)}\n"
        status += f"📊 文件大小: {file_size:.1f}MB\n"
        status += f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        status += f"🤖 使用模型: {_model_name}\n"
        status += "━" * 20 + "\n"

        # 检查是否使用合合OCR
        if model_name == "合合OCR":
            status += "🔄 正在使用合合OCR解析文档...\n"
            ocr_status, document_markdown = process_file_with_heheocr(file.name)

            if document_markdown is not None:
                end_time = time.time()
                processing_time = end_time - start_time

                status += f"{ocr_status}\n"
                status += f"⏱️ 处理时间: {processing_time:.1f}秒\n"
                status += f"📝 输出长度: {len(document_markdown)} 字符\n"

                return status, document_markdown
            else:
                return ocr_status, None
        
        # 确保模型已加载
        global global_llm, global_model_name
        if global_llm is None or global_model_name != model_name:
            status += "🔄 正在加载模型...\n"
            model_status, model_ready = initialize_model(model_name, gpu_memory, max_model_len)
            if not model_ready:
                return model_status, None
            status += f"✅ {model_status}\n"
        else:
            status += "✅ 模型已就绪\n"
        
        status += "🔄 正在解析文档...\n"
        
        # 解析文件
        result = parse(
            global_llm, 
            file.name,
            skip_cross_page_merge=skip_merge,
            max_page_retries=max_retries
        )
        
        if result is not None:
            end_time = time.time()
            processing_time = end_time - start_time
            
            document_markdown = result['document_text']
            num_pages = result.get('num_pages', 1)
            fallback_pages = result.get('fallback_pages', [])
            
            status += f"✅ 解析完成!\n"
            status += f"📖 处理页数: {num_pages}\n"
            if fallback_pages:
                status += f"⚠️ 失败页数: {len(fallback_pages)}\n"
            status += f"⏱️ 处理时间: {processing_time:.1f}秒\n"
            status += f"📝 输出长度: {len(document_markdown)} 字符\n"
            
            return status, document_markdown
        else:
            return "❌ 解析失败，请检查文件格式或重试", None
            
    except Exception as e:
        error_msg = f"❌ 处理过程中出现错误:\n{str(e)}\n\n详细错误信息:\n{traceback.format_exc()}"
        return error_msg, None

def preview_file(file):
    """预览上传的文件（支持PDF和图片格式）"""
    global current_preview_image, current_pdf_doc, current_page_index, total_pages
    
    if file is None:
        # 重置预览相关变量
        current_preview_image = None
        current_pdf_doc = None
        current_page_index = 0
        total_pages = 0
        return None, "📁 请上传文件进行预览", "页码: 0/0"
    
    try:
        file_ext = os.path.splitext(file.name)[1].lower()
        file_size = os.path.getsize(file.name) / (1024 * 1024)  # MB
        
        preview_info = f"📄 文件名: {os.path.basename(file.name)}\n"
        preview_info += f"📊 文件大小: {file_size:.2f} MB\n"
        preview_info += f"📋 文件类型: {file_ext.upper()}\n"
        preview_info += "━" * 20 + "\n"
        
        if file_ext == '.pdf':
            # PDF文件预览
            if not PDF_SUPPORT:
                preview_info += "⚠️ PDF预览功能不可用\n"
                preview_info += "原因: PyMuPDF库未安装\n"
                preview_info += "安装方法: pip install PyMuPDF\n"
                return None, preview_info + "📄 PDF文件已上传，但无法生成预览"
            
            try:
                doc = fitz.open(file.name)
                page_count = len(doc)
                preview_info += f"📖 总页数: {page_count}\n"
                
                # 保存PDF文档对象和页面信息
                current_pdf_doc = doc
                current_page_index = 0
                total_pages = page_count
                
                # 获取第一页作为预览
                if page_count > 0:
                    page = doc[0]
                    # 转换为图片，使用适中的缩放因子
                    mat = fitz.Matrix(1.2, 1.2)  # 缩放因子
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")
                    
                    # 保存临时图片文件
                    temp_path = tempfile.mktemp(suffix="_page_1.png")
                    with open(temp_path, "wb") as f:
                        f.write(img_data)
                    
                    # 设置预览相关变量
                    current_preview_image = temp_path
                    
                    preview_info += f"🖼️ 预览尺寸: {pix.width} x {pix.height}\n"
                    page_info = f"页码: {current_page_index + 1}/{total_pages}"
                    html_preview = create_simple_image_html(temp_path)
                    return html_preview, preview_info + "✅ PDF预览已生成（第1页）", page_info
                else:
                    current_pdf_doc = None
                    doc.close()
                    return None, preview_info + "❌ PDF文件为空", "页码: 0/0"
                    
            except Exception as e:
                return None, preview_info + f"❌ PDF预览失败: {str(e)}", "页码: 0/0"
                
        elif file_ext in ['.png', '.jpg', '.jpeg']:
            # 图片文件预览
            try:
                img = Image.open(file.name)
                width, height = img.size
                preview_info += f"🖼️ 图片尺寸: {width} x {height}\n"
                preview_info += f"🎨 图片模式: {img.mode}\n"
                
                # 设置预览相关变量
                current_preview_image = file.name
                current_pdf_doc = None  # 图片文件不是PDF
                current_page_index = 0
                total_pages = 1  # 图片只有一页
                
                # 如果图片太大，创建缩略图
                if width > 1200 or height > 1200:
                    img.thumbnail((1200, 1200), Image.Resampling.LANCZOS)
                    temp_path = tempfile.mktemp(suffix=file_ext)
                    img.save(temp_path)
                    current_preview_image = temp_path  # 更新为缩略图路径
                    page_info = "页码: 1/1"
                    html_preview = create_simple_image_html(temp_path)
                    return html_preview, preview_info + "✅ 图片预览已生成（已缩略）", page_info
                else:
                    page_info = "页码: 1/1"
                    html_preview = create_simple_image_html(file.name)
                    return html_preview, preview_info + "✅ 图片预览已生成", page_info
                    
            except Exception as e:
                return None, preview_info + f"❌ 图片预览失败: {str(e)}", "页码: 1/1"
        else:
            return None, preview_info + "❌ 不支持的文件格式", "页码: 0/0"
            
    except Exception as e:
        return None, f"❌ 文件预览失败: {str(e)}", "页码: 0/0"

# 全局变量存储当前预览状态
current_preview_image = None
current_pdf_doc = None
current_page_index = 0
total_pages = 0

def create_simple_image_html(image_path):
    """创建简单的HTML图像组件"""
    if not image_path or not os.path.exists(image_path):
        return "<div style='text-align:center; padding:40px; color:#666;'>📁 无预览图像</div>"
    
    try:
        # 获取图像实际尺寸
        img = Image.open(image_path)
        actual_width, actual_height = img.size
        
        # 创建HTML内容
        html_content = f"""
        <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            height: 500px;
            overflow: hidden;
        ">
            <img src="data:image/png;base64,{base64.b64encode(open(image_path,'rb').read()).decode()}" 
                 style="
                     max-width: 100%;
                     max-height: 100%;
                     object-fit: contain;
                     border-radius: 4px;
                     box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                 "
                 alt="预览图像"
            />
        </div>
        <div style="
            text-align: center;
            margin-top: 8px;
            font-size: 12px;
            color: #6c757d;
        ">
            原始尺寸: {actual_width} × {actual_height}
        </div>
        """
        
        return html_content
        
    except Exception as e:
        return f"<div style='text-align:center; padding:40px; color:#dc3545;'>❌ 图像加载失败: {str(e)}</div>"


def change_pdf_page(direction):
    """切换PDF页面"""
    global current_pdf_doc, current_page_index, total_pages, current_preview_image
    
    if not current_pdf_doc or total_pages == 0:
        return gr.update(), "📄 无PDF文件", "页码: 0/0"
    
    # 计算新的页面索引
    if direction == "prev" and current_page_index > 0:
        current_page_index -= 1
    elif direction == "next" and current_page_index < total_pages - 1:
        current_page_index += 1
    elif direction == "first":
        current_page_index = 0
    elif direction == "last":
        current_page_index = total_pages - 1
    
    try:
        # 获取当前页面
        page = current_pdf_doc[current_page_index]
        # 转换为图片
        mat = fitz.Matrix(1.2, 1.2)  # 缩放因子
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("png")
        
        # 保存临时图片文件
        temp_path = tempfile.mktemp(suffix=f"_page_{current_page_index + 1}.png")
        with open(temp_path, "wb") as f:
            f.write(img_data)
        
        current_preview_image = temp_path
        
        # 生成预览HTML
        html_preview = create_simple_image_html(temp_path)
        
        # 更新页面信息
        page_info = f"📖 第 {current_page_index + 1} 页，共 {total_pages} 页\n"
        page_info += f"🖼️ 预览尺寸: {pix.width} x {pix.height}\n"
        page_info += "✅ PDF页面预览已生成"
        
        page_counter = f"页码: {current_page_index + 1}/{total_pages}"
        
        return html_preview, page_info, page_counter
        
    except Exception as e:
        return gr.update(), f"❌ 页面切换失败: {str(e)}", f"页码: {current_page_index + 1}/{total_pages}"

def clear_all():
    """清除所有内容：上传文件、文件预览、解析结果和状态信息"""
    global current_preview_image, current_pdf_doc, current_page_index, total_pages
    
    # 重置预览相关变量
    current_preview_image = None
    if current_pdf_doc:
        current_pdf_doc.close()
    current_pdf_doc = None
    current_page_index = 0
    total_pages = 0
    
    return (
        None,  # file_input
        "<div style='text-align:center; padding:40px; color:#666;'>📁 请上传文件进行预览</div>",  # file_preview_html
        "📁 请上传文件进行预览",  # file_preview_info
        "🔄 已清除所有内容",  # status_text
        "<div style='text-align:center; padding:40px; color:#666;'>上传文件并点击'开始解析'按钮开始处理</div>",  # output_markdown
        "页码: 0/0"  # page_info_text
    )



# 分页控制函数

def prev_page():
    """上一页"""
    return change_pdf_page("prev")


def next_page():
    """下一页"""
    return change_pdf_page("next")


# 创建 Gradio 界面
with gr.Blocks(title="OCR 文档解析工具", theme=gr.themes.Soft()) as demo:
    # 紧凑的标题区域
    gr.Markdown("""
    <style>
    .page-btn {
        min-width: 100px !important;
        height: 32px !important;
        font-size: 14px !important;
    }
    </style>
    <div style='text-align: center; margin-bottom: 15px;'>
        <h1 style='font-size: 2.5em; color: #2E86AB; margin-bottom: 8px; font-weight: bold;'>
            🚀 OCR 文档解析工具
        </h1>
        <h2 style='font-size: 1.1em; color: #666; margin-bottom: 12px; font-weight: normal;'>
            智能解析 PDF 和图片文件，精准提取文本和表格结构
        </h2>
        <hr style='border: none; height: 2px; background: linear-gradient(90deg, #2E86AB, #A23B72, #F18F01); margin: 8px 0;'>
    </div>
    """)
    
    with gr.Row():
        # 上传文件栏（紧凑布局）
        with gr.Column(scale=1, min_width=280):
            gr.Markdown("### 📎 操作面板")
            
            file_input = gr.File(
                label="📄 上传文件",
                file_types=['.pdf', '.png', '.jpg', '.jpeg'],
                type="filepath",
                height=120
            )
            
            # 模型选择（移出高级设置）
            model_name = gr.Dropdown(
                choices=["OCRFlux", "合合OCR"],
                value="OCRFlux",
                label="模型选择"
            )
            
            with gr.Row():
                process_btn = gr.Button("开始解析", variant="primary", size="lg", scale=2)
                clear_btn = gr.Button("🗑️ 清除", variant="secondary", size="lg", scale=1)
            
            # 处理状态展示栏（可折叠）
            with gr.Accordion("🔄 处理状态", open=True):
                status_text = gr.Textbox(label="", lines=5, interactive=False)
            
            # 文件信息展示栏（可折叠）
            with gr.Accordion("📊 文件信息", open=False):
                file_preview_info = gr.Textbox(
                    label="",
                    lines=10,
                    interactive=False,
                    value="📁 请上传文件进行预览"
                )
        
        # 文件预览栏（更宽更长）
        with gr.Column(scale=2, min_width=400):
            gr.Markdown("### 🖼️ 文件预览")
            file_preview_html = gr.HTML(
                value="<div style='text-align:center; padding:40px; color:#666;'>📁 请上传文件进行预览</div>"
            )
            
            # PDF页面控制按钮
            # PDF页面控制按钮 - 保证同行显示
            with gr.Row(equal_height=True):
                prev_page_btn = gr.Button("◀️ 上一页", elem_classes=["page-btn"], size="sm")
                next_page_btn = gr.Button("下一页 ▶️", elem_classes=["page-btn"], size="sm")
            
            # 页码信息显示
            page_info_text = gr.Textbox(
                label="页码信息",
                lines=1,
                interactive=False,
                value="页码: 0/0"
            )
            

        # 解析结果栏（更宽更长）
        with gr.Column(scale=2, min_width=400):
            gr.Markdown("### 📝 解析结果")
            output_markdown = gr.Markdown(
                label="解析结果",
                value="<div style='text-align:center; padding:40px; color:#666;'>上传文件并点击'开始解析'按钮开始处理</div>",
                height=600
            )
            
            with gr.Row():
                download_btn = gr.DownloadButton(
                    label="下载 Markdown",
                    variant="secondary"
                )
                copy_btn = gr.Button("复制结果")
    
    # 绑定事件
    def update_download_button(markdown):
        if markdown and markdown.strip() and not markdown.startswith("<div style='text-align:center'"):
            with tempfile.NamedTemporaryFile(suffix=".md", mode='w', delete=False, encoding='utf-8') as f:
                f.write(markdown)
                return gr.update(visible=True, value=f.name)
        return gr.update(visible=False)
    
    def copy_to_clipboard(markdown):
        if markdown and not markdown.startswith("<div style='text-align:center'"):
            return gr.Info("已复制到剪贴板")
        return gr.Error("无内容可复制")
    
    # 创建一个包装函数，使用默认参数
    def process_file_with_defaults(file, model_name):
        """使用默认参数处理文件的包装函数"""
        # 将OCRFlux映射为实际的模型名称（合合OCR保持原名）
        actual_model_name = "ChatDOC/OCRFlux-3B" if model_name == "OCRFlux" else model_name

        return process_file(
            file=file,
            model_name=actual_model_name,
            gpu_memory=0.8,  # 默认GPU内存利用率
            max_model_len=8192,  # 默认最大上下文长度
            target_dim=1024,  # 默认目标尺寸
            max_retries=3,  # 默认最大重试次数
            skip_merge=True  # 默认跳过跨页合并
        )

    process_btn.click(
        fn=process_file_with_defaults,
        inputs=[file_input, model_name],
        outputs=[status_text, output_markdown]
    )
    
    output_markdown.change(
        fn=update_download_button,
        inputs=output_markdown,
        outputs=download_btn
    )
    
    copy_btn.click(
        fn=copy_to_clipboard,
        inputs=output_markdown,
        outputs=None
    )
    
    # 文件上传时自动预览
    file_input.change(
        fn=preview_file,
        inputs=file_input,
        outputs=[file_preview_html, file_preview_info, page_info_text]
    )
    
    # PDF页面切换按钮事件
    prev_page_btn.click(
        fn=prev_page,
        inputs=[],
        outputs=[file_preview_html, file_preview_info, page_info_text]
    )

    next_page_btn.click(
        fn=next_page,
        inputs=[],
        outputs=[file_preview_html, file_preview_info, page_info_text]
    )

    
    # 清除按钮事件
    clear_btn.click(
        fn=clear_all,
        inputs=[],
        outputs=[file_input, file_preview_html, file_preview_info, status_text, output_markdown, page_info_text]
    )

# 启动应用
if __name__ == "__main__":
    demo.launch(
        server_name='0.0.0.0', 
        share=False,
        show_api=False
        # pfavicon_path=os.path.join("images", "OCRFlux.png")
    )
