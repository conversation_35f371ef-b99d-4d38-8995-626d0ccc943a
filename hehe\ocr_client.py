import requests
import json
import time
import os
from typing import Dict, Optional

class OCRClient:
    """OCR API客户端"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        
    def create_task(self, file_path: str, **kwargs) -> Optional[str]:
        """创建解析任务"""
        url = f"{self.base_url}/api/contracts/v3/parser/external/task/create"
        
        # 默认参数
        params = {
            "parse_type": "document",
            "merge_images": 1,
            "char_details": 1,
            "dpi": 144,
            "apply_document_tree": 1,
            "markdown_details": 1,
            "table_flavor": "html",
            "get_image": "objects",
            "parse_mode": "scan"
        }
        params.update(kwargs)
        
        if not os.path.exists(file_path):
            return None
            
        try:
            with open(file_path, 'rb') as f:
                files = {'documents': f}
                response = requests.post(url, params=params, files=files)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        task_ids = result.get("data", {}).get("task_ids", [])
                        return task_ids[0] if task_ids else None
        except:
            pass
        return None
    
    def get_result(self, task_id: str) -> Optional[Dict]:
        """获取解析结果"""
        url = f"{self.base_url}/api/contracts/v3/parser/external/result"
        data = {"task_id": task_id}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None
    
    def wait_for_result(self, task_id: str, max_wait_time: int = 300) -> Optional[Dict]:
        """等待解析完成"""
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            result = self.get_result(task_id)
            if result:
                code = result.get("code")
                if code == 200:
                    return result
                elif code == 10703:
                    return None
                elif code != 10702:
                    return None
            time.sleep(5)
        return None
    
    def process_document(self, file_path: str, **kwargs) -> Optional[Dict]:
        """处理单个文档"""
        task_id = self.create_task(file_path, **kwargs)
        if not task_id:
            return None
        return self.wait_for_result(task_id)
    
    def process_folder(self, folder_path: str, output_folder: str = None) -> Dict:
        """处理文件夹中的所有文件"""
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            return {}
        
        if output_folder is None:
            output_folder = folder_path.rstrip('/') + "_output"
        os.makedirs(output_folder, exist_ok=True)
        
        # 支持的文件格式
        supported_formats = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp', '.gif'}
        
        # 获取所有文件
        files_to_process = []
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(filename)
                if ext.lower() in supported_formats:
                    files_to_process.append((filename, file_path))
        
        if not files_to_process:
            return {}
        
        print(f"开始处理 {len(files_to_process)} 个文件...")
        
        results = {}
        for i, (filename, file_path) in enumerate(files_to_process, 1):
            print(f"处理 {i}/{len(files_to_process)}: {filename}")
            
            result = self.process_document(file_path)
            
            if result and result.get("code") == 200:
                results[filename] = "success"
                
                # 保存结果
                output_file = os.path.join(output_folder, f"{filename}_result.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                
                # 保存文本
                data = result.get("data", {})
                parse_result = data.get("parse_result", {})
                detail = parse_result.get("detail", [])
                
                text_content = []
                for item in detail:
                    if item.get("type") == "paragraph":
                        text_content.append(item.get("text", ""))
                
                if text_content:
                    text_file = os.path.join(output_folder, f"{filename}_text.txt")
                    with open(text_file, "w", encoding="utf-8") as f:
                        f.write("\n".join(text_content))
                
                print(f"✓ {filename} 处理成功")
            else:
                results[filename] = "failed"
                print(f"✗ {filename} 处理失败")
        
        success_count = sum(1 for status in results.values() if status == "success")
        print(f"处理完成: {success_count}/{len(files_to_process)} 成功")
        print(f"结果保存在: {output_folder}")
        
        return results

    def export_markdown(self, task_ids: list) -> Optional[bytes]:
        """导出markdown文件"""
        url = f"{self.base_url}/api/contracts/v3/parser/external/md_file/export"
        data = {"task_ids": task_ids}
        
        try:
            response = requests.post(url, json=data)
            if response.status_code == 200:
                return response.content
        except:
            pass
        return None

    def process_document_with_markdown(self, file_path: str, **kwargs) -> Optional[Dict]:
        """处理单个文档并导出markdown"""
        task_id = self.create_task(file_path, **kwargs)
        if not task_id:
            return None
        
        result = self.wait_for_result(task_id)
        if result and result.get("code") == 200:
            # 导出markdown
            markdown_content = self.export_markdown([task_id])
            return {
                "result": result,
                "markdown_content": markdown_content,
                "task_id": task_id
            }
        return None

    def process_folder_with_markdown(self, folder_path: str, output_folder: str = None) -> Dict:
        """处理文件夹中的所有文件并保存markdown"""
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            return {}
        
        if output_folder is None:
            output_folder = folder_path.rstrip('/') + "_output"
        os.makedirs(output_folder, exist_ok=True)
        
        # 支持的文件格式
        supported_formats = {'.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.bmp', '.gif'}
        
        # 获取所有文件
        files_to_process = []
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                _, ext = os.path.splitext(filename)
                if ext.lower() in supported_formats:
                    files_to_process.append((filename, file_path))
        
        if not files_to_process:
            return {}
        
        print(f"开始处理 {len(files_to_process)} 个文件...")
        
        results = {}
        for i, (filename, file_path) in enumerate(files_to_process, 1):
            print(f"处理 {i}/{len(files_to_process)}: {filename}")
            
            result_data = self.process_document_with_markdown(file_path)
            
            if result_data and result_data["result"].get("code") == 200:
                results[filename] = "success"
                
                # 保存JSON结果
                output_file = os.path.join(output_folder, f"{filename}_result.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result_data["result"], f, ensure_ascii=False, indent=2)
                
                # 保存markdown文件
                if result_data["markdown_content"]:
                    markdown_file = os.path.join(output_folder, f"{filename}.md")
                    with open(markdown_file, "wb") as f:
                        f.write(result_data["markdown_content"])
                
                # 保存文本
                data = result_data["result"].get("data", {})
                parse_result = data.get("parse_result", {})
                detail = parse_result.get("detail", [])
                
                text_content = []
                for item in detail:
                    if item.get("type") == "paragraph":
                        text_content.append(item.get("text", ""))
                
                if text_content:
                    text_file = os.path.join(output_folder, f"{filename}_text.txt")
                    with open(text_file, "w", encoding="utf-8") as f:
                        f.write("\n".join(text_content))
                
                print(f"✓ {filename} 处理成功（包含markdown）")
            else:
                results[filename] = "failed"
                print(f"✗ {filename} 处理失败")
        
        success_count = sum(1 for status in results.values() if status == "success")
        print(f"处理完成: {success_count}/{len(files_to_process)} 成功")
        print(f"结果保存在: {output_folder}")
        
        return results
