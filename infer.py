from vllm import LLM
from ocrflux.inference import parse

file_path = 'test.pdf'
# file_path = 'test.png'
llm = LLM(model="ChatDOC/OCRFlux-3B",
          tensor_parallel_size=1,
          enforce_eager=True,
          dtype="float16",
          gpu_memory_utilization=0.8,
          max_model_len=8192)
result = parse(llm,file_path)
if result != None:
    document_markdown = result['document_text']
    print(document_markdown)
    with open('test.md','w') as f:
        f.write(document_markdown)
else:
    print("Parse failed.")