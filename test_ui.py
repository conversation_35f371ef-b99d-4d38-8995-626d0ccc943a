# 简单测试界面修改是否成功
import gradio as gr

# 模拟的处理函数
def mock_process_file(file, model_name, gpu_memory, max_model_len, target_dim, max_retries, skip_merge):
    """模拟的文件处理函数"""
    if file is None:
        return "⚠️ 请先上传文件", None
    return f"✅ 使用模型 {model_name} 处理文件成功", "这是模拟的解析结果"

def mock_process_file_with_defaults(file, model_name):
    """使用默认参数处理文件的包装函数"""
    return mock_process_file(
        file=file,
        model_name=model_name,
        gpu_memory=0.8,  # 默认GPU内存利用率
        max_model_len=8192,  # 默认最大上下文长度
        target_dim=1024,  # 默认目标尺寸
        max_retries=3,  # 默认最大重试次数
        skip_merge=True  # 默认跳过跨页合并
    )

# 创建简化的界面
with gr.Blocks(title="OCRFlux 测试界面") as demo:
    gr.Markdown("# 🚀 OCRFlux 文档解析工具（简化版）")
    
    with gr.Row():
        with gr.Column(scale=1):
            gr.Markdown("### 📎 操作面板")
            
            file_input = gr.File(
                label="📄 上传文件",
                file_types=['.pdf', '.png', '.jpg', '.jpeg'],
                type="filepath"
            )
            
            # 只保留模型选择，移除高级设置
            model_name = gr.Dropdown(
                choices=["ChatDOC/OCRFlux-3B", "合合OCR"],
                value="ChatDOC/OCRFlux-3B",
                label="模型选择"
            )
            
            process_btn = gr.Button("开始解析", variant="primary")
            
            status_text = gr.Textbox(label="处理状态", lines=3, interactive=False)
        
        with gr.Column(scale=2):
            gr.Markdown("### 📝 解析结果")
            output_markdown = gr.Markdown(
                label="解析结果",
                value="上传文件并点击'开始解析'按钮开始处理"
            )
    
    # 绑定事件 - 使用默认参数
    process_btn.click(
        fn=mock_process_file_with_defaults,
        inputs=[file_input, model_name],
        outputs=[status_text, output_markdown]
    )

if __name__ == "__main__":
    demo.launch(server_name='127.0.0.1', share=False)
