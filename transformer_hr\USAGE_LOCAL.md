# 使用本地模型路径运行OCRFlux (NPU版本)

## 概述

您的代码已经修改为使用本地缓存的模型文件，避免网络连接问题。

## 当前配置

所有文件已配置为使用以下本地模型路径：
```
/root/.cache/huggingface/hub/models--ChatDOC--OCRFlux-3B
```

并设置了 `local_files_only=True` 以确保不会尝试从网络下载。

## 运行方式

### 1. 检查NPU环境
```bash
cd transformer_hr
python check_npu_env.py
```

### 2. 运行示例
```bash
python example_transformers.py
```

### 3. 或者运行主程序
```bash
python transformer_hf.py
```

## 如果模型路径不同

如果您的模型保存在不同位置，请修改以下文件中的 `model_path` 参数：

1. **example_transformers.py** (第24行)
2. **transformer_hf.py** (第357行) 
3. **inference_hf.py** (第349行)

将路径改为您的实际模型路径，例如：
```python
model_path="/your/actual/path/to/model"
```

## 验证模型路径

确认您的模型路径包含以下文件：
```
/root/.cache/huggingface/hub/models--ChatDOC--OCRFlux-3B/
├── config.json
├── model.safetensors (或 pytorch_model.bin)
├── tokenizer.json
├── tokenizer_config.json
└── 其他模型文件...
```

## 常见问题

### 1. 路径不存在
如果提示路径不存在，请检查：
- 模型是否已完全下载
- 路径是否正确
- 是否有访问权限

### 2. 模型加载失败
如果模型加载失败，请：
- 确认模型文件完整
- 检查NPU环境是否正确配置
- 查看错误日志获取详细信息

### 3. NPU不可用
如果NPU不可用，代码会自动回退到CPU/CUDA模式。

## 性能优化

- NPU内存利用率设置为0.8，可根据实际情况调整
- 每5个查询会自动清理NPU内存
- 使用float16精度以节省内存

## 日志输出

运行时会显示详细的加载和处理信息：
- ✅ 成功操作
- ⚠️ 警告信息  
- ❌ 错误信息
- 🔍 检测信息
- 💾 内存使用信息
