import base64
import torch
from io import BytesIO
from PIL import Image
from transformers import AutoTokenizer, AutoProcessor, AutoModelForImageTextToText

def pillow_to_base64(image):
    """
    将PIL图像对象转换为base64编码字符串
    
    Args:
        image: PIL.Image对象
        
    Returns:
        bytes: base64编码的字节串
    """
    # 创建内存中的字节流缓冲区
    img_buffer = BytesIO()
    # 将图像保存为JPEG格式到缓冲区
    image.save(img_buffer, format='JPEG')
    # 获取缓冲区中的字节数据
    byte_data = img_buffer.getvalue()
    # 将字节数据编码为base64格式
    base64_str = base64.b64encode(byte_data)
    return base64_str

def build_page_to_markdown_prompt() -> str:
    return (
        f"Below is the image of one page of a document. "
        f"Just return the plain text representation of this document as if you were reading it naturally.\n"
        f"ALL tables should be presented in HTML format.\n"
        f"If there are images or figures in the page, present them as \"<Image>(left,top),(right,bottom)</Image>\", (left,top,right,bottom) are the coordinates of the top-left and bottom-right corners of the image or figure.\n"
        f"Present all titles and headings as H1 headings.\n"
        f"Do not hallucinate.\n"
    )

model_path = "ChatDOC/OCRFlux-3B"

# 加载预训练的图像文本生成模型
model = AutoModelForImageTextToText.from_pretrained(
    model_path, 
    torch_dtype="auto",  # 自动选择数据类型
    device_map="auto",   # 自动分配设备（GPU/CPU）
    # attn_implementation="flash_attention_2"  # 可选：使用Flash Attention 2优化
    attn_implementation=None  # 使用默认注意力机制
)
# 设置模型为评估模式（关闭dropout等训练特性）
model.eval()

# 加载对应的分词器
tokenizer = AutoTokenizer.from_pretrained(model_path)
# 加载图像和文本处理器
processor = AutoProcessor.from_pretrained(model_path)



prompt = build_page_to_markdown_prompt()
image_path = 'test.jpg'
max_new_tokens = 8192

# 加载图像文件
image = Image.open(image_path)
# 将图像转换为base64编码
image_base64_str = pillow_to_base64(image)

# 构建对话消息格式，符合模型的输入要求
messages = [
    {"role": "system", "content": "You are a helpful assistant."},  # 系统角色设定
    {"role": "user", "content": [  # 用户输入内容
        # {"type": "image", "image": f"file://{image_path}"},  # 文件路径方式（已注释）
        {"type": "image", "image": f"data:image;base64,{image_base64_str}"},  # base64编码方式
        {"type": "text", "text": prompt},  # 文本提示词
    ]},
]
# 应用聊天模板，将消息格式化为模型可理解的文本格式
text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

# 处理输入：将文本和图像转换为模型输入格式
inputs1 = processor(text=[text], images=[image], padding=True, return_tensors="pt")
# 将输入数据移动到模型所在的设备（GPU/CPU）
inputs = inputs1.to(model.device)

# 生成文本输出
output_ids = model.generate(
    **inputs, 
    max_new_tokens=max_new_tokens,  # 最大生成token数
    do_sample=False  # 使用贪婪解码（确定性输出）
)

# 提取生成的新token（去除输入部分）
generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]

# 将生成的token解码为文本
output_text = processor.batch_decode(
    generated_ids, 
    skip_special_tokens=True,  # 跳过特殊token
    clean_up_tokenization_spaces=True  # 清理分词空格
)

# 打印输出结果
print(output_text)