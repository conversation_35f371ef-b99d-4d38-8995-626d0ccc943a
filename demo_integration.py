# 演示合合OCR集成效果
import gradio as gr
import sys
import os

# 添加hehe目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'hehe'))

# 模拟的合合OCR处理函数
def mock_process_file_with_heheocr(file_path):
    """模拟合合OCR处理文件"""
    import time
    time.sleep(2)  # 模拟处理时间
    
    # 模拟返回markdown内容
    mock_markdown = f"""# 文档解析结果

## 文件信息
- 文件名: {os.path.basename(file_path)}
- 处理引擎: 合合OCR
- 处理时间: 2.0秒

## 解析内容

这是使用**合合OCR**解析的示例内容。

### 表格示例
| 项目 | 数值 | 备注 |
|------|------|------|
| 准确率 | 98.5% | 高精度 |
| 速度 | 2.0s | 快速处理 |

### 文本内容
这里是从文档中提取的文本内容，包含了各种格式的文字、数字和符号。

**注意**: 这是演示内容，实际使用时会调用真实的合合OCR API。
"""
    
    return "✅ 合合OCR处理成功", mock_markdown

def mock_process_file_with_ocrflux(file_path):
    """模拟OCRFlux处理文件"""
    import time
    time.sleep(3)  # 模拟处理时间
    
    # 模拟返回markdown内容
    mock_markdown = f"""# OCRFlux 解析结果

## 文件信息
- 文件名: {os.path.basename(file_path)}
- 处理引擎: OCRFlux (ChatDOC/OCRFlux-3B)
- 处理时间: 3.0秒

## 解析内容

这是使用**OCRFlux**模型解析的示例内容。

### 特点
- 高精度文本识别
- 表格结构保持
- 多语言支持

### 示例表格
| 功能 | OCRFlux | 合合OCR |
|------|---------|---------|
| 精度 | 99.2% | 98.5% |
| 速度 | 中等 | 快速 |
| 成本 | 本地部署 | API调用 |

**注意**: 这是演示内容，实际使用时会调用真实的OCRFlux模型。
"""
    
    return "✅ OCRFlux处理成功", mock_markdown

def demo_process_file(file, model_name):
    """演示文件处理函数"""
    if file is None:
        return "⚠️ 请先上传文件", None
    
    import time
    from datetime import datetime
    
    start_time = time.time()
    
    # 显示处理状态
    status = f"📄 处理文件: {os.path.basename(file.name)}\n"
    status += f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    status += f"🤖 使用模型: {model_name}\n"
    status += "━" * 20 + "\n"
    
    # 根据模型选择调用不同的处理函数
    if model_name == "合合OCR":
        status += "🔄 正在使用合合OCR解析文档...\n"
        ocr_status, document_markdown = mock_process_file_with_heheocr(file.name)
    else:  # OCRFlux
        status += "🔄 正在使用OCRFlux解析文档...\n"
        ocr_status, document_markdown = mock_process_file_with_ocrflux(file.name)
    
    if document_markdown is not None:
        end_time = time.time()
        processing_time = end_time - start_time
        
        status += f"{ocr_status}\n"
        status += f"⏱️ 处理时间: {processing_time:.1f}秒\n"
        status += f"📝 输出长度: {len(document_markdown)} 字符\n"
        
        return status, document_markdown
    else:
        return ocr_status, None

# 创建演示界面
with gr.Blocks(title="OCRFlux 集成演示", theme=gr.themes.Soft()) as demo:
    gr.Markdown("""
    # 🚀 OCRFlux + 合合OCR 集成演示
    
    这个演示展示了如何在界面中集成两种OCR引擎：
    - **OCRFlux**: 本地部署的高精度模型
    - **合合OCR**: 云端API服务
    """)
    
    with gr.Row():
        with gr.Column(scale=1):
            gr.Markdown("### 📎 操作面板")
            
            file_input = gr.File(
                label="📄 上传文件",
                file_types=['.pdf', '.png', '.jpg', '.jpeg'],
                type="filepath"
            )
            
            model_name = gr.Dropdown(
                choices=["OCRFlux", "合合OCR"],
                value="OCRFlux",
                label="模型选择"
            )
            
            process_btn = gr.Button("开始解析", variant="primary")
            
            status_text = gr.Textbox(label="处理状态", lines=8, interactive=False)
        
        with gr.Column(scale=2):
            gr.Markdown("### 📝 解析结果")
            output_markdown = gr.Markdown(
                label="解析结果",
                value="上传文件并选择模型，然后点击'开始解析'按钮",
                height=500
            )
    
    # 绑定事件
    process_btn.click(
        fn=demo_process_file,
        inputs=[file_input, model_name],
        outputs=[status_text, output_markdown]
    )

if __name__ == "__main__":
    demo.launch(server_name='127.0.0.1', share=False)
