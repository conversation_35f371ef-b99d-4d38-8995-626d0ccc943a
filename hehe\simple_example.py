#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的OCR文件夹处理示例
"""

from ocr_client import OCRClient

def main():
    # 配置API服务器地址
    API_BASE_URL = "http://117.131.156.84:10070"
    
    # 创建OCR客户端
    client = OCRClient(API_BASE_URL)
    
    # 指定要处理的文件夹路径
    folder_path = "First"
    
    # 指定输出路径
    output_folder = "output/First"
    
    # 处理文件夹并保存markdown
    results = client.process_folder_with_markdown(folder_path, output_folder)
    
    if results:
        print("处理完成！已保存JSON、文本和Markdown文件")
    else:
        print("没有找到可处理的文件")

if __name__ == "__main__":
    main() 
