"""
使用Transformers框架的OCR处理示例
这是transformer.py的Transformers版本，功能完全等效
"""

from transformer_hf import TransformersLLMWrapper
from inference_hf import parse

def main():
    """
    主函数 - 使用Transformers框架进行OCR处理
    完全等效于原始transformer.py的功能
    """
    print("🚀 启动基于Transformers的OCR处理...")
    
    # 文件路径配置（与原始代码相同）
    file_path = 'avce-20-2023.pdf'
    # file_path = 'test.jpg'
    
    # 创建LLM实例（等效于vLLM的参数配置）
    # gpu_memory_utilization=0.8 -> 通过量化和显存优化实现
    # max_model_len=8192 -> 通过生成参数控制
    llm = TransformersLLMWrapper(
        model_path="ChatDOC/OCRFlux-3B",
        gpu_memory_utilization=0.8,
        max_model_len=8192
    )
    
    # 调用parse函数（接口完全兼容）
    result = parse(llm, file_path)
    
    # 处理结果（与原始代码相同）
    if result != None:
        document_markdown = result['document_text']
        print("📄 OCR处理结果:")
        print(document_markdown)
        
        # 保存到文件
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
        print("💾 结果已保存到 test.md")
    else:
        print("❌ Parse failed.")


if __name__ == "__main__":
    main()
