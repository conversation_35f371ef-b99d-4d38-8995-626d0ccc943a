"""
OCR processing example using Transformers framework
This is the Transformers version of transformer.py with completely equivalent functionality
"""

from transformer_hf import TransformersLLM<PERSON><PERSON>per
from inference_hf import parse

def main():
    """
    Main function - OCR processing using Transformers framework
    Completely equivalent to the functionality of original transformer.py
    """
    print("🚀 Starting Transformers-based OCR processing...")
    
    # File path configuration (same as original code)
    file_path = 'avce-20-2023.pdf'
    # file_path = 'test.jpg'
    
    # Create LLM instance (equivalent to vLLM parameter configuration)
    # gpu_memory_utilization=0.8 -> Implemented through quantization and GPU memory optimization
    # max_model_len=8192 -> Controlled through generation parameters
    llm = TransformersLLMWrapper(
        model_path="ChatDOC/OCRFlux-3B",
        gpu_memory_utilization=0.8,
        max_model_len=8192
    )
    
    # Call parse function (interface fully compatible)
    result = parse(llm, file_path)
    
    # Process results (same as original code)
    if result != None:
        document_markdown = result['document_text']
        print("📄 OCR processing results:")
        print(document_markdown)
        
        # Save to file
        with open('test.md', 'w', encoding='utf-8') as f:
            f.write(document_markdown)
        print("💾 Results saved to test.md")
    else:
        print("❌ Parse failed.")


if __name__ == "__main__":
    main()
